import { jsx as _jsx } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Config, ApprovalMode } from './config/config.js';
import { CredentialsManager } from './auth/credentials.js';
import { ProviderManager } from './providers/manager.js';
import { ToolRegistry } from './tools/tool-registry.js';
import { useTheme } from './hooks/useTheme.js';
import { AppLayout } from './components/layout/AppLayout.js';
import { AuthScreen } from './components/auth/AuthScreen.js';
import { ThemeSelector } from './components/theme/ThemeSelector.js';
import { ChatContainer } from './components/chat/ChatContainer.js';
import { LoadingScreen } from './components/ui/LoadingScreen.js';
import { discoverMcpTools } from './tools/mcp-client.js';
const App = () => {
    const [appState, setAppState] = useState('loading');
    const [config] = useState(() => new Config({
        cwd: process.cwd(),
        model: 'gemini-1.5-pro',
        embeddingModel: 'text-embedding-004',
        sandbox: false,
        targetDir: process.cwd(),
        debugMode: false,
        userMemory: '',
        geminiMdFileCount: 50,
        approvalMode: ApprovalMode.DEFAULT,
        sessionId: Date.now().toString(),
        fileFilteringRespectGitIgnore: true,
        fullContext: false
    }));
    const [credentialsManager] = useState(() => new CredentialsManager());
    const [providerManager] = useState(() => new ProviderManager());
    const [toolRegistry] = useState(() => new ToolRegistry(config));
    const { themeManager, setTheme } = useTheme();
    // Initialize the application
    useEffect(() => {
        const initializeApp = async () => {
            try {
                // Discover MCP tools
                await discoverMcpTools(config.getMcpServers() || {}, config.getMcpServerCommand(), toolRegistry);
                // Check if user is already authenticated
                const configuredProviders = credentialsManager.getConfiguredProviders();
                if (configuredProviders.length > 0) {
                    const defaultProvider = credentialsManager.getDefaultProvider() || configuredProviders[0];
                    const providerConfig = credentialsManager.getProviderConfig(defaultProvider);
                    if (providerConfig.apiKey && providerConfig.model) {
                        // Create and set the provider
                        const providerId = providerManager.createProvider(defaultProvider, {
                            apiKey: providerConfig.apiKey,
                            model: providerConfig.model,
                            baseUrl: providerConfig.baseUrl,
                        });
                        providerManager.setCurrentProvider(providerId);
                        // Skip to theme selection or chat
                        setAppState('theme');
                        return;
                    }
                }
                // Start with authentication
                setAppState('auth');
            }
            catch (error) {
                console.error('Failed to initialize app:', error);
                setAppState('auth');
            }
        };
        initializeApp();
    }, [config, credentialsManager, providerManager, toolRegistry]);
    const handleAuthComplete = () => {
        setAppState('theme');
    };
    const handleThemeSelected = (themeName) => {
        setTheme(themeName);
        setAppState('chat');
    };
    const handleThemeSkipped = () => {
        setAppState('chat');
    };
    const renderCurrentState = () => {
        switch (appState) {
            case 'loading':
                return (_jsx(LoadingScreen, { themeManager: themeManager, status: "Initializing Arien AI CLI...", onComplete: () => {
                        // Loading complete, proceed to next state
                        if (!credentialsManager.hasValidCredentials()) {
                            setAppState('auth');
                        }
                        else if (!themeManager.isThemeSelected()) {
                            setAppState('theme');
                        }
                        else {
                            setAppState('chat');
                        }
                    } }));
            case 'auth':
                return (_jsx(AuthScreen, { themeManager: themeManager, credentialsManager: credentialsManager, providerManager: providerManager, onAuthComplete: handleAuthComplete }));
            case 'theme':
                return (_jsx(ThemeSelector, { themeManager: themeManager, onThemeSelected: handleThemeSelected, onSkip: handleThemeSkipped }));
            case 'chat':
                return (_jsx(ChatContainer, { themeManager: themeManager, providerManager: providerManager, toolRegistry: toolRegistry }));
            default:
                return (_jsx(LoadingScreen, { themeManager: themeManager, status: "Unknown application state...", onComplete: () => setAppState('loading') }));
        }
    };
    return (_jsx(AppLayout, { themeManager: themeManager, children: renderCurrentState() }));
};
export default App;
//# sourceMappingURL=App.js.map
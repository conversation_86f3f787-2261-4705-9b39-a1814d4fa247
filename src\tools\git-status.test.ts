/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { GitStatusTool, GitStatusParams } from './git-status.js';
import { Config, ApprovalMode, ConfigParameters } from '../config/config.js';
import fs from 'fs';
import { spawn } from 'child_process';

// Mock fs
vi.mock('fs');
const mockFs = vi.mocked(fs);

// Mock child_process
vi.mock('child_process');
const mockSpawn = vi.mocked(spawn);

// Mock config
const mockConfigParams: ConfigParameters = {
  cwd: '/test/project',
  model: 'test-model',
  embeddingModel: 'test-embedding',
  sandbox: false,
  targetDir: '/test/project',
  debugMode: false,
  userMemory: '',
  geminiMdFileCount: 50,
  approvalMode: ApprovalMode.DEFAULT,
  sessionId: 'test-session',
  fileFilteringRespectGitIgnore: true,
  fullContext: false,
};

describe('GitStatusTool', () => {
  let gitStatusTool: GitStatusTool;
  let mockConfig: Config;
  let mockProcess: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockConfig = new Config(mockConfigParams);
    gitStatusTool = new GitStatusTool(mockConfig);

    // Mock process object
    mockProcess = {
      stdout: {
        on: vi.fn(),
      },
      stderr: {
        on: vi.fn(),
      },
      on: vi.fn(),
    };

    mockSpawn.mockReturnValue(mockProcess as any);
    mockFs.existsSync.mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should create GitStatusTool with correct properties', () => {
      expect(gitStatusTool.name).toBe('git_status');
      expect(gitStatusTool.displayName).toBe('Git Status');
      expect(gitStatusTool.description).toContain('Shows the working tree status');
      expect(gitStatusTool.isOutputMarkdown).toBe(true);
      expect(gitStatusTool.canUpdateOutput).toBe(false);
    });

    it('should have correct schema structure', () => {
      const schema = gitStatusTool.schema;
      expect(schema.name).toBe('git_status');
      expect(schema.parameters).toBeDefined();
      expect(schema.parameters.properties).toHaveProperty('directory');
      expect(schema.parameters.properties).toHaveProperty('porcelain');
      expect(schema.parameters.properties).toHaveProperty('show_ignored');
    });
  });

  describe('validateToolParams', () => {
    it('should validate correct parameters', () => {
      const params: GitStatusParams = {
        directory: 'src',
        porcelain: false,
        show_ignored: false,
      };

      const result = gitStatusTool.validateToolParams(params);
      expect(result).toBeNull();
    });

    it('should reject absolute directory paths', () => {
      const params: GitStatusParams = {
        directory: '/absolute/path',
      };

      const result = gitStatusTool.validateToolParams(params);
      expect(result).toContain('Directory cannot be absolute');
    });

    it('should reject non-existent directories', () => {
      mockFs.existsSync.mockReturnValue(false);
      
      const params: GitStatusParams = {
        directory: 'nonexistent',
      };

      const result = gitStatusTool.validateToolParams(params);
      expect(result).toContain('Directory must exist');
    });

    it('should accept empty parameters', () => {
      const params: GitStatusParams = {};
      const result = gitStatusTool.validateToolParams(params);
      expect(result).toBeNull();
    });
  });

  describe('execute', () => {
    it('should handle clean repository status', async () => {
      const params: GitStatusParams = {};
      const abortSignal = new AbortController().signal;

      // Mock git rev-parse success
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      // Mock git status output for clean repo
      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            // First call is rev-parse (empty output)
            setTimeout(() => callback(''), 0);
          } else {
            // Second call is status
            setTimeout(() => callback('## main\n'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Working tree clean');
      expect(result.llmContent).toContain('**Branch:** main');
      expect(result.returnDisplay).toContain('Clean');
    });

    it('should handle repository with changes', async () => {
      const params: GitStatusParams = {};
      const abortSignal = new AbortController().signal;

      // Mock git commands success
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            // rev-parse output
            setTimeout(() => callback(''), 0);
          } else {
            // git status output with changes
            const statusOutput = `## main
M  src/file1.ts
 M src/file2.ts
A  src/file3.ts
?? src/file4.ts`;
            setTimeout(() => callback(statusOutput), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('**Branch:** main');
      expect(result.llmContent).toContain('Staged Changes');
      expect(result.llmContent).toContain('Unstaged Changes');
      expect(result.llmContent).toContain('Untracked Files');
      expect(result.llmContent).toContain('src/file1.ts');
      expect(result.llmContent).toContain('src/file2.ts');
      expect(result.llmContent).toContain('src/file3.ts');
      expect(result.llmContent).toContain('src/file4.ts');
      expect(result.returnDisplay).toContain('Changes detected');
    });

    it('should handle porcelain format', async () => {
      const params: GitStatusParams = { porcelain: true };
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            setTimeout(() => callback('## main\n'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      // Should return JSON format for porcelain
      expect(result.llmContent).toContain('"branch": "main"');
      expect(result.llmContent).toContain('"clean": true');
    });

    it('should handle not a git repository error', async () => {
      const params: GitStatusParams = {};
      const abortSignal = new AbortController().signal;

      // Mock git rev-parse failure
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(1), 0); // Exit code 1 for failure
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('fatal: not a git repository'), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Not a git repository');
      expect(result.returnDisplay).toContain('Not a git repository');
    });

    it('should handle cancelled operation', async () => {
      const params: GitStatusParams = {};
      const abortController = new AbortController();
      abortController.abort();

      const result = await gitStatusTool.execute(params, abortController.signal);

      expect(result.llmContent).toContain('cancelled by user');
      expect(result.returnDisplay).toContain('cancelled');
    });

    it('should handle validation errors', async () => {
      const params: GitStatusParams = {
        directory: '/absolute/path',
      };
      const abortSignal = new AbortController().signal;

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Error:');
      expect(result.llmContent).toContain('Directory cannot be absolute');
      expect(result.returnDisplay).toContain('Git Status Error');
    });

    it('should handle git status command failure', async () => {
      const params: GitStatusParams = {};
      const abortSignal = new AbortController().signal;

      let callCount = 0;
      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          callCount++;
          if (callCount === 1) {
            // rev-parse succeeds
            setTimeout(() => callback(0), 0);
          } else {
            // git status fails
            setTimeout(() => callback(1), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback('git status error'), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('Git status failed');
      expect(result.returnDisplay).toContain('Git status error');
    });
  });

  describe('status parsing', () => {
    it('should parse branch with tracking information', async () => {
      const params: GitStatusParams = {};
      const abortSignal = new AbortController().signal;

      mockProcess.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 0);
        }
      });

      let callCount = 0;
      mockProcess.stdout.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          callCount++;
          if (callCount === 1) {
            setTimeout(() => callback(''), 0);
          } else {
            // Branch with tracking info
            setTimeout(() => callback('## feature...origin/feature [ahead 2, behind 1]\n'), 0);
          }
        }
      });

      mockProcess.stderr.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'data') {
          setTimeout(() => callback(''), 0);
        }
      });

      const result = await gitStatusTool.execute(params, abortSignal);

      expect(result.llmContent).toContain('**Branch:** feature');
      expect(result.llmContent).toContain('ahead 2');
      expect(result.llmContent).toContain('behind 1');
    });
  });
});
